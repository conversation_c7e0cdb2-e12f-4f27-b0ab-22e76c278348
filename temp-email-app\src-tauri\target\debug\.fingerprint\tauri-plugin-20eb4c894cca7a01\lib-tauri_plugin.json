{"rustc": 1842507548689473721, "features": "[\"build\"]", "declared_features": "[\"build\", \"runtime\"]", "target": 15996119522804316622, "profile": 2225463790103693989, "path": 9817291777889714938, "deps": [[654232091421095663, "tauri_utils", false, 13336498927337277973], [6913375703034175521, "schemars", false, 16571058717946061746], [9689903380558560274, "serde", false, 14955956804811053645], [13625485746686963219, "anyhow", false, 16521415306001099308], [15609422047640926750, "toml", false, 3677411513436772130], [15622660310229662834, "walkdir", false, 4450029676203649764], [16362055519698394275, "serde_json", false, 15741321141703459404], [17155886227862585100, "glob", false, 16666287207728800018]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-plugin-20eb4c894cca7a01\\dep-lib-tauri_plugin", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}