use serde::{Deserialize, Serialize};
use std::collections::HashMap;

// API响应数据结构
#[derive(Debug, Serialize, Deserialize)]
pub struct ApiResponse<T> {
    pub code: i32,
    pub data: T,
    pub msg: String,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct Domain {
    pub id: i32,
    pub domain: String,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct DomainsData {
    pub domains: Vec<Domain>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct EmailData {
    pub email: String,
    #[serde(rename = "isCustomize")]
    pub is_customize: String,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct EmailMessage {
    pub id: i32,
    pub subject: String,
    #[serde(rename = "messageId")]
    pub message_id: String,
    #[serde(rename = "fromMail")]
    pub from_mail: String,
    #[serde(rename = "toMail")]
    pub to_mail: String,
    #[serde(rename = "createdAt")]
    pub created_at: String,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct MessagesData {
    pub emails: Vec<EmailMessage>,
    pub total: i32,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct EmailDetail {
    pub id: i32,
    pub subject: String,
    #[serde(rename = "messageId")]
    pub message_id: String,
    #[serde(rename = "fromMail")]
    pub from_mail: String,
    #[serde(rename = "toMail")]
    pub to_mail: String,
    #[serde(rename = "htmlContent")]
    pub html_content: String,
    #[serde(rename = "textContent")]
    pub text_content: String,
    #[serde(rename = "rawContent")]
    pub raw_content: String,
    pub date: String,
    #[serde(rename = "otherParts")]
    pub other_parts: Option<serde_json::Value>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct EmailDetailData {
    #[serde(rename = "emailDetail")]
    pub email_detail: EmailDetail,
}

// API常量
const API_BASE_URL: &str = "https://temp-email-api.sitestage.top/v1";
const AUTHORIZATION_TOKEN: &str = "Bearer cxj12345";

// 生成随机邮箱名称（4-8位字母）
fn generate_random_name() -> String {
    use rand::Rng;
    const CHARSET: &[u8] = b"abcdefghijklmnopqrstuvwxyz";
    let mut rng = rand::thread_rng();
    let len = rng.gen_range(4..=8);

    (0..len)
        .map(|_| {
            let idx = rng.gen_range(0..CHARSET.len());
            CHARSET[idx] as char
        })
        .collect()
}

// 获取域名列表
#[tauri::command]
async fn get_domains() -> Result<Vec<Domain>, String> {
    let client = reqwest::Client::new();
    let url = format!("{}/domains", API_BASE_URL);

    let response = client
        .get(&url)
        .header("Authorization", AUTHORIZATION_TOKEN)
        .send()
        .await
        .map_err(|e| format!("请求失败: {}", e))?;

    let api_response: ApiResponse<DomainsData> = response
        .json()
        .await
        .map_err(|e| format!("解析响应失败: {}", e))?;

    if api_response.code == 200 {
        Ok(api_response.data.domains)
    } else {
        Err(format!("API错误: {}", api_response.msg))
    }
}

// 创建临时邮箱
#[tauri::command]
async fn create_email(domain_id: i32, custom_name: Option<String>) -> Result<String, String> {
    let client = reqwest::Client::new();
    let url = format!("{}/email", API_BASE_URL);
    let name = custom_name.unwrap_or_else(|| generate_random_name());

    let mut payload = HashMap::new();
    payload.insert("name", name);
    payload.insert("domainId", domain_id.to_string());

    let response = client
        .post(&url)
        .header("Authorization", AUTHORIZATION_TOKEN)
        .header("Content-Type", "application/json")
        .json(&payload)
        .send()
        .await
        .map_err(|e| format!("请求失败: {}", e))?;

    // 先获取响应文本进行调试
    let response_text = response
        .text()
        .await
        .map_err(|e| format!("读取响应失败: {}", e))?;

    if response_text.is_empty() {
        return Err("API返回空响应".to_string());
    }

    // 尝试解析JSON
    let api_response: ApiResponse<EmailData> = serde_json::from_str(&response_text)
        .map_err(|e| format!("解析响应失败: {} - 响应内容: {}", e, response_text))?;

    if api_response.code == 200 {
        Ok(api_response.data.email)
    } else {
        Err(format!("API错误: {}", api_response.msg))
    }
}

// 获取邮件列表
#[tauri::command]
async fn get_messages(email: String) -> Result<Vec<EmailMessage>, String> {
    let client = reqwest::Client::new();
    let url = format!("{}/messages?limit=100&offset=1&email={}", API_BASE_URL, email);

    let response = client
        .get(&url)
        .header("Authorization", AUTHORIZATION_TOKEN)
        .send()
        .await
        .map_err(|e| format!("请求失败: {}", e))?;

    let api_response: ApiResponse<MessagesData> = response
        .json()
        .await
        .map_err(|e| format!("解析响应失败: {}", e))?;

    if api_response.code == 200 {
        Ok(api_response.data.emails)
    } else {
        Err(format!("API错误: {}", api_response.msg))
    }
}

// 获取邮件详情并提取验证码
#[tauri::command]
async fn get_verification_code(message_id: String) -> Result<String, String> {
    let client = reqwest::Client::new();
    // URL编码message_id
    let encoded_message_id = urlencoding::encode(&message_id);
    let url = format!("{}/messages/{}", API_BASE_URL, encoded_message_id);

    let response = client
        .get(&url)
        .header("Authorization", AUTHORIZATION_TOKEN)
        .send()
        .await
        .map_err(|e| format!("请求失败: {}", e))?;

    let api_response: ApiResponse<EmailDetailData> = response
        .json()
        .await
        .map_err(|e| format!("解析响应失败: {}", e))?;

    if api_response.code == 200 {
        let html_content = &api_response.data.email_detail.html_content;

        // 使用正则表达式提取验证码
        if let Some(code) = extract_verification_code(html_content) {
            Ok(code)
        } else {
            Err("未找到验证码".to_string())
        }
    } else {
        Err(format!("API错误: {}", api_response.msg))
    }
}

// 从HTML内容中提取验证码
fn extract_verification_code(html_content: &str) -> Option<String> {
    // 查找 "Your verification code is: " 后面的数字
    if let Some(start) = html_content.find("Your verification code is:") {
        let after_text = &html_content[start + 26..]; // 26是"Your verification code is:"的长度

        // 查找数字模式
        for word in after_text.split_whitespace() {
            // 移除HTML标签和特殊字符，只保留数字
            let clean_word: String = word.chars()
                .filter(|c| c.is_ascii_digit())
                .collect();

            if clean_word.len() >= 4 && clean_word.len() <= 8 {
                return Some(clean_word);
            }
        }
    }

    None
}

#[cfg_attr(mobile, tauri::mobile_entry_point)]
pub fn run() {
    tauri::Builder::default()
        .plugin(tauri_plugin_opener::init())
        .invoke_handler(tauri::generate_handler![
            get_domains,
            create_email,
            get_messages,
            get_verification_code
        ])
        .run(tauri::generate_context!())
        .expect("error while running tauri application");
}
