# 临时邮箱助手

一个基于 Tauri 开发的跨平台临时邮箱应用程序，支持 Windows 和 macOS。

## 功能特性

- 🌐 支持多个邮箱域名选择
- 📧 自动生成随机临时邮箱地址
- 🔄 实时刷新邮件列表
- 🔢 自动提取验证码
- 📋 一键复制邮箱地址和验证码
- 🎨 现代化用户界面，支持深色模式
- 🖥️ 跨平台支持（Windows、macOS）

## 技术栈

- **前端**: Vue 3 + TypeScript + Vite
- **后端**: Rust + Tauri
- **HTTP 客户端**: reqwest
- **UI**: 原生 CSS，响应式设计

## 开发环境要求

### 通用要求
- Node.js 16+
- npm 或 yarn

### Windows
- Rust (通过 rustup 安装)
- Microsoft Visual Studio C++ Build Tools

### macOS
- Rust (通过 rustup 安装)
- Xcode Command Line Tools

## 安装和运行

### 1. 安装依赖
```bash
npm install
```

### 2. 开发模式运行
```bash
npm run tauri:dev
```

### 3. 构建生产版本
```bash
# 构建当前平台的发布版本
npm run tauri:build

# 构建调试版本（更快）
npm run tauri:build:debug
```

## 构建输出

构建完成后，可执行文件将位于：
- **Windows**: `src-tauri/target/release/temp-email-app.exe`
- **macOS**: `src-tauri/target/release/bundle/macos/临时邮箱助手.app`

## API 配置

应用程序使用以下 API 端点：
- 基础 URL: `https://temp-email-api.sitestage.top/v1`
- 认证令牌: `Bearer cxj12345`

### API 端点
- `GET /domains` - 获取可用域名列表
- `POST /email` - 创建临时邮箱
- `GET /messages` - 获取邮件列表
- `GET /messages/{messageId}` - 获取邮件详情

## 使用说明

1. **选择域名**: 从下拉列表中选择一个邮箱域名
2. **生成邮箱**: 点击"生成邮箱"按钮创建临时邮箱地址
3. **复制邮箱**: 点击邮箱地址旁的"复制"按钮
4. **刷新邮件**: 点击"刷新邮件"查看收到的邮件
5. **获取验证码**: 点击"获取验证码"从最新邮件中提取验证码
6. **复制验证码**: 点击验证码旁的"复制"按钮

## 许可证

本项目仅供学习和研究使用。
