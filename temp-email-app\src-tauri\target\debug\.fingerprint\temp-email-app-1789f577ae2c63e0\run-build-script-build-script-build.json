{"rustc": 1842507548689473721, "features": "", "declared_features": "", "target": 0, "profile": 0, "path": 0, "deps": [[12092653563678505622, "build_script_build", false, 8556069227368867460], [16702348383442838006, "build_script_build", false, 6535846346157131837], [16199841968381804945, "build_script_build", false, 17576564867141595066]], "local": [{"RerunIfChanged": {"output": "debug\\build\\temp-email-app-1789f577ae2c63e0\\output", "paths": ["tauri.conf.json", "capabilities"]}}, {"RerunIfEnvChanged": {"var": "TAURI_CONFIG", "val": null}}, {"RerunIfEnvChanged": {"var": "REMOVE_UNUSED_COMMANDS", "val": null}}], "rustflags": [], "config": 0, "compile_kind": 0}