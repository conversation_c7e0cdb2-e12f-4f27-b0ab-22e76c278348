{"rustc": 1842507548689473721, "features": "[\"drag-drop\", \"gdkx11\", \"javascriptcore-rs\", \"linux-body\", \"os-webview\", \"protocol\", \"soup3\", \"webkit2gtk\", \"webkit2gtk-sys\", \"x11\", \"x11-dl\"]", "declared_features": "[\"default\", \"devtools\", \"drag-drop\", \"fullscreen\", \"gdkx11\", \"javascriptcore-rs\", \"linux-body\", \"mac-proxy\", \"os-webview\", \"protocol\", \"serde\", \"soup3\", \"tracing\", \"transparent\", \"webkit2gtk\", \"webkit2gtk-sys\", \"x11\", \"x11-dl\"]", "target": 2463569863749872413, "profile": 11987593577772629573, "path": 1313671789237867526, "deps": [[2013030631243296465, "webview2_com", false, 6727071469576301027], [3334271191048661305, "windows_version", false, 11468952044839301626], [3722963349756955755, "once_cell", false, 14521976164538839542], [4143744114649553716, "raw_window_handle", false, 14178324387943116473], [5628259161083531273, "windows_core", false, 7460213642378104539], [7606335748176206944, "dpi", false, 10125233705909480676], [9010263965687315507, "http", false, 15629939712500996649], [9141053277961803901, "build_script_build", false, 13882620257616778741], [10806645703491011684, "thiserror", false, 6923350312884292379], [11989259058781683633, "dunce", false, 11426863789755588728], [14585479307175734061, "windows", false, 8091520080730486835], [16727543399706004146, "cookie", false, 13002483928728026112]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\wry-8c07836d04e9e0ae\\dep-lib-wry", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}