<script setup lang="ts">
import { ref, onMounted, onUnmounted } from "vue";
import { invoke } from "@tauri-apps/api/core";

// 数据类型定义
interface Domain {
  id: number;
  domain: string;
}

interface EmailMessage {
  id: number;
  subject: string;
  message_id: string;
  from_mail: string;
  to_mail: string;
  created_at: string;
}

// 响应式数据
const domains = ref<Domain[]>([]);
const selectedDomainId = ref<number | null>(null);
const customName = ref<string>("");
const useCustomName = ref<boolean>(false);
const currentEmail = ref<string>("");
const messages = ref<EmailMessage[]>([]);
const verificationCode = ref<string>("");
const loading = ref<boolean>(false);
const error = ref<string>("");
const autoRefresh = ref<boolean>(false);
const refreshInterval = ref<NodeJS.Timeout | null>(null);

// 获取域名列表
async function loadDomains() {
  try {
    loading.value = true;
    error.value = "";
    const result = await invoke<Domain[]>("get_domains");
    domains.value = result;
    if (result.length > 0) {
      selectedDomainId.value = result[0].id;
    }
  } catch (err) {
    error.value = `获取域名失败: ${err}`;
  } finally {
    loading.value = false;
  }
}

// 生成临时邮箱
async function generateEmail() {
  if (!selectedDomainId.value) {
    error.value = "请选择一个域名";
    return;
  }

  try {
    loading.value = true;
    error.value = "";
    const customNameValue = useCustomName.value && customName.value.trim()
      ? customName.value.trim()
      : null;

    const result = await invoke<string>("create_email", {
      domainId: selectedDomainId.value,
      customName: customNameValue
    });
    currentEmail.value = result;
    messages.value = [];
    verificationCode.value = "";

    // 开始自动刷新
    if (autoRefresh.value) {
      startAutoRefresh();
    }
  } catch (err) {
    error.value = `生成邮箱失败: ${err}`;
  } finally {
    loading.value = false;
  }
}

// 刷新邮件列表
async function refreshMessages() {
  if (!currentEmail.value) {
    error.value = "请先生成邮箱";
    return;
  }

  try {
    loading.value = true;
    error.value = "";
    const result = await invoke<EmailMessage[]>("get_messages", {
      email: currentEmail.value
    });
    messages.value = result;
  } catch (err) {
    error.value = `获取邮件失败: ${err}`;
  } finally {
    loading.value = false;
  }
}

// 获取验证码
async function getVerificationCode() {
  if (messages.value.length === 0) {
    error.value = "没有邮件可以获取验证码";
    return;
  }

  const latestMessage = messages.value[0];

  try {
    loading.value = true;
    error.value = "";
    const result = await invoke<string>("get_verification_code", {
      messageId: latestMessage.message_id
    });
    verificationCode.value = result;
  } catch (err) {
    error.value = `获取验证码失败: ${err}`;
  } finally {
    loading.value = false;
  }
}

// 复制到剪贴板
async function copyToClipboard(text: string) {
  try {
    await navigator.clipboard.writeText(text);
    // 简单的提示，可以后续改进
    alert("已复制到剪贴板");
  } catch (err) {
    error.value = "复制失败";
  }
}

// 自动刷新功能
function startAutoRefresh() {
  if (refreshInterval.value) {
    clearInterval(refreshInterval.value);
  }

  refreshInterval.value = setInterval(async () => {
    if (currentEmail.value && !loading.value) {
      await refreshMessages();
      if (messages.value.length > 0 && !verificationCode.value) {
        await getVerificationCode();
      }
    }
  }, 5000); // 每5秒刷新一次
}

function stopAutoRefresh() {
  if (refreshInterval.value) {
    clearInterval(refreshInterval.value);
    refreshInterval.value = null;
  }
}

// 切换自动刷新
function toggleAutoRefresh() {
  autoRefresh.value = !autoRefresh.value;
  if (autoRefresh.value && currentEmail.value) {
    startAutoRefresh();
  } else {
    stopAutoRefresh();
  }
}

// 组件挂载时加载域名
onMounted(() => {
  loadDomains();
});

// 组件卸载时清理定时器
onUnmounted(() => {
  stopAutoRefresh();
});
</script>

<template>
  <main class="container">
    <h1>临时邮箱助手</h1>

    <!-- 错误提示 -->
    <div v-if="error" class="error-message">
      {{ error }}
    </div>

    <!-- 邮箱生成区域 -->
    <div class="email-generator">
      <div class="email-input-group">
        <div class="name-input">
          <label>
            <input type="checkbox" v-model="useCustomName"> 自定义前缀
          </label>
          <input
            v-if="useCustomName"
            v-model="customName"
            type="text"
            placeholder="输入前缀"
            maxlength="20"
            :disabled="loading"
          >
          <span v-else class="random-text">随机生成</span>
        </div>

        <span class="at-symbol">@</span>

        <select v-model="selectedDomainId" :disabled="loading" class="domain-select">
          <option v-for="domain in domains" :key="domain.id" :value="domain.id">
            {{ domain.domain }}
          </option>
        </select>

        <button @click="generateEmail" :disabled="loading || !selectedDomainId" class="generate-btn">
          {{ loading ? '生成中...' : '生成' }}
        </button>
      </div>
    </div>

    <!-- 当前邮箱显示 -->
    <div v-if="currentEmail" class="current-email">
      <div class="email-display">
        <span class="email-text">{{ currentEmail }}</span>
        <button @click="copyToClipboard(currentEmail)" class="copy-btn">复制</button>
      </div>

      <div class="controls">
        <button @click="refreshMessages" :disabled="loading" class="control-btn">
          {{ loading ? '刷新中...' : '刷新邮件' }}
        </button>
        <label class="auto-refresh">
          <input type="checkbox" v-model="autoRefresh" @change="toggleAutoRefresh">
          自动获取验证码
        </label>
      </div>
    </div>

    <!-- 验证码显示 -->
    <div v-if="verificationCode" class="verification-section">
      <h3>验证码</h3>
      <div class="verification-code">
        <span class="code-text">{{ verificationCode }}</span>
        <button @click="copyToClipboard(verificationCode)" class="copy-btn">复制</button>
      </div>
    </div>

    <!-- 最新邮件显示 -->
    <div v-if="messages.length > 0" class="latest-message">
      <h3>最新邮件</h3>
      <div class="message-item">
        <div class="message-subject">{{ messages[0].subject }}</div>
        <div class="message-from">来自: {{ messages[0].from_mail }}</div>
        <div class="message-time">{{ new Date(messages[0].created_at).toLocaleString() }}</div>
      </div>
    </div>

    <!-- 加载指示器 -->
    <div v-if="loading" class="loading">
      处理中...
    </div>
  </main>
</template>

<style scoped>
.container {
  max-width: 600px;
  margin: 0 auto;
  padding: 20px;
}

.email-generator {
  background: #ffffff;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  margin-bottom: 20px;
}

.email-input-group {
  display: flex;
  align-items: center;
  gap: 8px;
  flex-wrap: wrap;
}

.name-input {
  display: flex;
  flex-direction: column;
  gap: 8px;
  min-width: 120px;
}

.name-input label {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 14px;
  color: #666;
}

.name-input input[type="text"] {
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 6px;
  font-size: 14px;
}

.random-text {
  padding: 8px 12px;
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 6px;
  font-size: 14px;
  color: #6c757d;
  font-style: italic;
}

.at-symbol {
  font-size: 18px;
  font-weight: bold;
  color: #495057;
}

.domain-select {
  min-width: 160px;
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 6px;
  font-size: 14px;
}

.generate-btn {
  background: #28a745;
  color: white;
  border: none;
  padding: 8px 20px;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
}

.generate-btn:hover:not(:disabled) {
  background: #218838;
}

.current-email {
  background: #ffffff;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  margin-bottom: 20px;
}

.email-display {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 16px;
  background: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #e9ecef;
  margin-bottom: 16px;
}

.email-text {
  font-family: 'Courier New', monospace;
  font-size: 16px;
  font-weight: bold;
  color: #2c3e50;
  flex: 1;
  word-break: break-all;
}

.controls {
  display: flex;
  align-items: center;
  gap: 16px;
  flex-wrap: wrap;
}

.control-btn {
  background: #007bff;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 6px;
  font-size: 14px;
  cursor: pointer;
}

.control-btn:hover:not(:disabled) {
  background: #0056b3;
}

.auto-refresh {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 14px;
  color: #666;
  cursor: pointer;
}

.verification-section, .latest-message {
  background: #ffffff;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  margin-bottom: 20px;
}

.verification-code {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px;
  background: #e8f5e8;
  border-radius: 8px;
  border: 2px solid #28a745;
}

.code-text {
  font-family: 'Courier New', monospace;
  font-size: 24px;
  font-weight: bold;
  color: #155724;
  flex: 1;
  text-align: center;
  letter-spacing: 2px;
}

.copy-btn {
  background: #007bff;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
}

.copy-btn:hover {
  background: #0056b3;
}

.message-item {
  padding: 16px;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  background: #f8f9fa;
}

.message-subject {
  font-weight: bold;
  color: #2c3e50;
  margin-bottom: 8px;
  font-size: 16px;
}

.message-from, .message-time {
  font-size: 14px;
  color: #6c757d;
  margin-bottom: 4px;
}

.error-message {
  background: #f8d7da;
  color: #721c24;
  padding: 12px 16px;
  border-radius: 8px;
  border: 1px solid #f5c6cb;
  margin-bottom: 20px;
  font-size: 14px;
}

.loading {
  text-align: center;
  padding: 20px;
  color: #6c757d;
  font-style: italic;
}

h1 {
  color: #2c3e50;
  text-align: center;
  margin-bottom: 30px;
  font-size: 28px;
}

h3 {
  color: #495057;
  margin-bottom: 16px;
  font-size: 18px;
}

@media (max-width: 600px) {
  .email-input-group {
    flex-direction: column;
    align-items: stretch;
  }

  .name-input {
    min-width: auto;
  }

  .controls {
    flex-direction: column;
    align-items: stretch;
  }
}
</style>
<style>
:root {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  font-size: 16px;
  line-height: 1.5;
  font-weight: 400;
  color: #2c3e50;
  background-color: #f8f9fa;
  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  -webkit-text-size-adjust: 100%;
}

* {
  box-sizing: border-box;
}

body {
  margin: 0;
  padding: 0;
  min-height: 100vh;
}

input,
button,
select {
  border-radius: 6px;
  border: 1px solid #ced4da;
  padding: 8px 12px;
  font-size: 14px;
  font-family: inherit;
  color: #495057;
  background-color: #ffffff;
  transition: all 0.2s ease;
}

button {
  cursor: pointer;
  background-color: #007bff;
  color: white;
  border-color: #007bff;
  font-weight: 500;
}

button:hover:not(:disabled) {
  background-color: #0056b3;
  border-color: #0056b3;
}

button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

select {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
  background-position: right 8px center;
  background-repeat: no-repeat;
  background-size: 16px;
  padding-right: 32px;
}

input:focus,
button:focus,
select:focus {
  outline: none;
  border-color: #007bff;
  box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
}

@media (prefers-color-scheme: dark) {
  :root {
    color: #e9ecef;
    background-color: #212529;
  }

  .section {
    background: #343a40 !important;
    border-color: #495057 !important;
  }

  .email-display, .verification-code, .message-item {
    background: #495057 !important;
    border-color: #6c757d !important;
  }

  .email-text, .code-text {
    color: #e9ecef !important;
  }

  input, select {
    background-color: #495057;
    border-color: #6c757d;
    color: #e9ecef;
  }

  .error-message {
    background: #721c24;
    color: #f8d7da;
    border-color: #a94442;
  }
}
</style>