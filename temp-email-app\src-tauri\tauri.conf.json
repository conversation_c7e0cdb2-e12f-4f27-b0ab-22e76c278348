{"$schema": "https://schema.tauri.app/config/2", "productName": "临时邮箱助手", "version": "0.1.0", "identifier": "com.temp-email-helper.app", "build": {"beforeDevCommand": "npm run dev", "devUrl": "http://localhost:1420", "beforeBuildCommand": "npm run build", "frontendDist": "../dist"}, "app": {"windows": [{"title": "临时邮箱助手", "width": 650, "height": 550, "resizable": true, "minimizable": true, "maximizable": true, "minWidth": 500, "minHeight": 400}], "security": {"csp": null}}, "bundle": {"active": true, "targets": "all", "icon": ["icons/32x32.png", "icons/128x128.png", "icons/<EMAIL>", "icons/icon.icns", "icons/icon.ico"], "category": "Utility", "shortDescription": "临时邮箱助手", "longDescription": "一个用于生成临时邮箱和获取验证码的实用工具，支持多个邮箱域名。", "copyright": "Copyright © 2025", "licenseFile": null, "windows": {"certificateThumbprint": null, "digestAlgorithm": "sha256", "timestampUrl": null}, "macOS": {"frameworks": [], "minimumSystemVersion": "10.13", "exceptionDomain": null, "signingIdentity": null, "providerShortName": null, "entitlements": null}}}