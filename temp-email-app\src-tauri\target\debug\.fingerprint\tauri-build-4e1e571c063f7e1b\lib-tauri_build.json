{"rustc": 1842507548689473721, "features": "[\"config-json\", \"default\"]", "declared_features": "[\"codegen\", \"config-json\", \"config-json5\", \"config-toml\", \"default\", \"isolation\", \"quote\", \"tauri-codegen\"]", "target": 1006236803848883740, "profile": 2225463790103693989, "path": 15561909567757332897, "deps": [[654232091421095663, "tauri_utils", false, 13336498927337277973], [1140993874832966859, "tauri_winres", false, 15073761797512552386], [4824857623768494398, "cargo_toml", false, 6511347916706011766], [4899080583175475170, "semver", false, 16636169827845809847], [6913375703034175521, "schemars", false, 16571058717946061746], [7170110829644101142, "json_patch", false, 11884101678998366027], [9689903380558560274, "serde", false, 14955956804811053645], [13077543566650298139, "heck", false, 12227043807642275620], [13625485746686963219, "anyhow", false, 16521415306001099308], [15609422047640926750, "toml", false, 3677411513436772130], [15622660310229662834, "walkdir", false, 4450029676203649764], [16362055519698394275, "serde_json", false, 15741321141703459404], [16928111194414003569, "dirs", false, 3626647766493926319], [17155886227862585100, "glob", false, 16666287207728800018]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-build-4e1e571c063f7e1b\\dep-lib-tauri_build", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}